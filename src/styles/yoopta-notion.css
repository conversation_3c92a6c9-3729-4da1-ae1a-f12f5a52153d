/* Yoopta Editor Notion-style Customization */
.yoopta-selection-block {
  height: 5px;
}

/* Main editor container - clean, minimal like Notion */
.notion-editor-container {
  position: relative;
  width: 100% !important;
  max-width: 100% !important;
  min-height: 500px;
  background: transparent;
  cursor: text;
  box-sizing: border-box !important;
  display: block !important;
  flex: 1 1 100% !important;
}

.notion-content-area {
  width: 100% !important;
  max-width: 100% !important;
  min-height: 60vh;
  background: transparent;
  padding: 0;
  margin: 0;
  box-sizing: border-box !important;
}

/* Ensure editor container is clickable and focusable with full width */
.notion-editor-container {
  position: relative;
  width: 100% !important;
  max-width: 100% !important;
  min-height: 500px;
  background: transparent;
  cursor: text;
  outline: none;
  box-sizing: border-box !important;
}

.notion-editor-container:focus-within {
  outline: none;
}

/* Improve click-to-focus behavior */
.notion-editor-container:empty {
  min-height: 500px;
  cursor: text;
}

.notion-editor-container:empty::before {
  content: '';
  display: block;
  height: 100%;
  width: 100%;
  cursor: text;
}

/* Enhanced click-to-focus behavior */
.notion-editor-container {
  cursor: text;
  position: relative;
}

/* Make empty areas clickable */
.notion-editor-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

/* When editor has content, make the bottom area clickable */
.notion-editor-container:not(:empty)::after {
  pointer-events: auto;
  min-height: 100px;
  top: auto;
  bottom: 0;
  height: 100px;
  cursor: text;
}

/* Ensure blocks are clickable */
.notion-style-editor .yoopta-block {
  cursor: text;
  position: relative;
}

/* Visual feedback for hovering over blocks */
.notion-style-editor .yoopta-block:hover {
  background-color: rgba(55, 53, 47, 0.03);
  border-radius: 3px;
  transition: background-color 0.1s ease;
}

/* Focus state for blocks */
.notion-style-editor .yoopta-block:focus-within {
  background-color: rgba(55, 53, 47, 0.05);
  border-radius: 3px;
}

.notion-editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: rgba(55, 53, 47, 0.65);
  font-size: 14px;
  font-style: italic;
}

.notion-style-editor {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  line-height: 1;
  color: rgb(55, 53, 47);
  min-height: 500px;
  position: relative;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

/* Remove all default editor styling and ensure full width */
.notion-style-editor .yoopta-editor,
.notion-style-editor [data-yoopta-editor] {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;
  min-height: 500px;
  cursor: text;
  width: 100% !important;
  max-width: 100% !important;
}

/* Enhanced contenteditable styling - exactly like Notion with full width */
.notion-style-editor [contenteditable="true"] {
  outline: none !important;
  cursor: text;
  caret-color: rgb(55, 53, 47);
  line-height: 1;
  font-size: 16px;
  color: rgb(55, 53, 47);
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  min-height: 1em;
  word-wrap: break-word;
  white-space: pre-wrap;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  position: relative;
}

.notion-style-editor [contenteditable="true"]:focus {
  outline: none !important;
  caret-color: rgb(55, 53, 47);
}

/* Ensure proper cursor behavior on Enter key */
.notion-style-editor [contenteditable="true"]:focus-within {
  caret-color: rgb(55, 53, 47) !important;
}

/* Improve click-to-focus precision */
.notion-style-editor [contenteditable="true"]:hover {
  background-color: rgba(55, 53, 47, 0.02);
  border-radius: 2px;
  transition: background-color 0.1s ease;
}

/* Visual indicator for focused content */
.notion-style-editor [contenteditable="true"]:focus {
  background-color: rgba(55, 53, 47, 0.03);
  border-radius: 2px;
}

/* Fix cursor positioning after line breaks */
.notion-style-editor br {
  line-height: 1;
}

.notion-style-editor div:empty::after {
  content: '';
  display: inline-block;
  width: 0;
}

/* Placeholder styling - subtle like Notion */
.notion-style-editor [data-yoopta-editor]:empty::before,
.notion-style-editor [contenteditable="true"]:empty::before {
  content: attr(data-placeholder);
  color: rgba(55, 53, 47, 0.4);
  pointer-events: none;
  position: absolute;
  font-size: 16px;
  line-height: 1;
}

/* Text selection - clean blue like Notion */
.notion-style-editor ::selection {
  background-color: rgba(46, 170, 220, 0.2);
  color: inherit;
}

/* Block styling - clean and minimal like Notion with full width */
.notion-style-editor .yoopta-block {
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  border-radius: 3px;
  position: relative;
  transition: background-color 0.1s ease;
  line-height: 1;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor .yoopta-block:hover {
  background-color: rgba(55, 53, 47, 0.06) !important;
}

.notion-style-editor .yoopta-block-selected {
  background-color: rgba(46, 170, 220, 0.1) !important;
}

.notion-style-editor .yoopta-block-focused {
  outline: none !important;
  background-color: transparent !important;
}

/* Block handles (drag indicators) */
.notion-style-editor .yoopta-block-handle {
  opacity: 0;
  transition: opacity 0.1s ease;
  cursor: grab;
  color: rgb(156, 163, 175);
}

.notion-style-editor .yoopta-block:hover .yoopta-block-handle {
  opacity: 1;
}

/* Typography - exact Notion spacing and sizing with full width */
.notion-style-editor h1 {
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  line-height: 1.1 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor h2 {
  font-size: 1.875rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
  margin: 2px 0 1px 0 !important;
  color: rgb(55, 53, 47) !important;
  padding: 3px 2px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor p {
  font-size: 16px !important;
  line-height: 1.3 !important;
  color: rgb(55, 53, 47) !important;
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  min-height: 1.3em !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Lists - Notion-style spacing with full width */
.notion-style-editor ul, .notion-style-editor ol {
  margin: 1px 0 !important;
  padding-left: 1.5rem !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.notion-style-editor li {
  margin: 1px 0 !important;
  padding: 3px 2px !important;
  line-height: 1.3 !important;
  color: rgb(55, 53, 47) !important;
  font-size: 16px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Blockquotes - Notion style with full width */
.notion-style-editor blockquote {
  border-left: 3px solid rgb(55, 53, 47) !important;
  padding: 3px 2px 3px 16px !important;
  margin: 1px 0 !important;
  font-style: normal !important;
  color: rgb(55, 53, 47) !important;
  font-size: 16px !important;
  line-height: 1.3 !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Code blocks */
.notion-style-editor pre {
  background: rgb(247, 246, 243) !important;
  border: 1px solid rgb(227, 226, 224) !important;
  border-radius: 3px !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
  overflow-x: auto !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
}

.notion-style-editor code {
  background: rgba(135, 131, 120, 0.15) !important;
  color: #eb5757 !important;
  border-radius: 3px !important;
  padding: 0.2em 0.4em !important;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
  font-size: 85% !important;
}

/* Dividers */
.notion-style-editor hr {
  border: none !important;
  border-top: 1px solid rgb(227, 226, 224) !important;
  margin: 1rem 0 !important;
}

/* Images */
.notion-style-editor img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 3px !important;
  margin: 0.5rem 0 !important;
}

/* Yoopta Image Plugin Styles */
.notion-style-editor .yoopta-image {
  margin: 1rem 0 !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

.notion-style-editor .yoopta-image-uploader {
  border: 2px dashed rgb(209, 213, 219) !important;
  border-radius: 6px !important;
  padding: 2rem !important;
  text-align: center !important;
  background: rgb(249, 250, 251) !important;
  transition: all 0.2s ease !important;
}

.notion-style-editor .yoopta-image-uploader:hover {
  border-color: rgb(59, 130, 246) !important;
  background: rgb(239, 246, 255) !important;
}

.notion-style-editor .yoopta-image-placeholder {
  color: rgb(107, 114, 128) !important;
  font-size: 14px !important;
}

.notion-style-editor .yoopta-image-embed-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid rgb(209, 213, 219) !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}

.notion-style-editor .yoopta-image-embed-input:focus {
  outline: none !important;
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Links */
.notion-style-editor a {
  color: rgb(46, 170, 220) !important;
  text-decoration: underline !important;
  text-decoration-color: rgba(46, 170, 220, 0.4) !important;
  transition: text-decoration-color 0.1s ease !important;
}

.notion-style-editor a:hover {
  text-decoration-color: rgb(46, 170, 220) !important;
}

/* Callouts */
.notion-style-editor .yoopta-callout {
  background: rgb(241, 245, 249) !important;
  border: 1px solid rgb(226, 232, 240) !important;
  border-radius: 3px !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
}

/* Tables */
.notion-style-editor table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 0.5rem 0 !important;
}

.notion-style-editor th,
.notion-style-editor td {
  border: 1px solid rgb(227, 226, 224) !important;
  padding: 0.5rem !important;
  text-align: left !important;
}

.notion-style-editor th {
  background: rgb(247, 246, 243) !important;
  font-weight: 600 !important;
}

/* Action Menu (Slash Commands) */
.yoopta-action-menu {
  background: white !important;
  border: 1px solid rgb(227, 226, 224) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  padding: 0.5rem 0 !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.yoopta-action-menu-item {
  padding: 0.5rem 1rem !important;
  cursor: pointer !important;
  transition: background-color 0.1s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

.yoopta-action-menu-item:hover,
.yoopta-action-menu-item-selected {
  background: rgb(247, 246, 243) !important;
}

/* Toolbar */
.yoopta-toolbar {
  background: white !important;
  border: 1px solid rgb(227, 226, 224) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  padding: 0.25rem !important;
  display: flex !important;
  gap: 0.25rem !important;
}

.yoopta-toolbar-button {
  padding: 0.375rem !important;
  border-radius: 3px !important;
  border: none !important;
  background: transparent !important;
  cursor: pointer !important;
  transition: background-color 0.1s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.yoopta-toolbar-button:hover {
  background: rgb(247, 246, 243) !important;
}

.yoopta-toolbar-button-active {
  background: rgb(46, 170, 220) !important;
  color: white !important;
}

/* Placeholder text */
.notion-style-editor .yoopta-placeholder {
  color: rgb(156, 163, 175) !important;
  font-style: normal !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notion-style-editor {
    /* color: rgb(229, 231, 235) !important; */
  }
  
  .notion-style-editor .yoopta-block:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }
  
  .notion-style-editor h1,
  .notion-style-editor h2,
  .notion-style-editor h3,
  .notion-style-editor p {
    /* color: rgb(229, 231, 235) !important; */
  }
  
  .notion-style-editor pre {
    background: rgb(31, 41, 55) !important;
    border-color: rgb(55, 65, 81) !important;
  }
  
  .notion-style-editor .yoopta-placeholder {
    color: rgb(107, 114, 128) !important;
  }
}

/* Focus states */
.notion-style-editor .yoopta-block-focused {
  outline: none !important;
}

/* Selection styling */
.notion-style-editor ::selection {
  background: rgba(46, 170, 220, 0.2) !important;
}

/* Smooth animations */
.notion-style-editor * {
  transition: background-color 0.1s ease, color 0.1s ease !important;
}

/* Keyboard interaction improvements */
.notion-style-editor {
  /* Ensure proper keyboard navigation */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Full width enforcement for all block elements */
.notion-style-editor > *,
.notion-style-editor .yoopta-block > *,
.notion-style-editor [data-yoopta-block] > *,
.notion-style-editor [data-yoopta-element] {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure proper line break behavior */
.notion-style-editor br {
  line-height: 1.3 !important;
  display: block !important;
  content: "" !important;
  margin: 0 !important;
}

/* Handle empty blocks properly for keyboard navigation */
.notion-style-editor [contenteditable="true"]:empty {
  min-height: 1.3em !important;
}

.notion-style-editor [contenteditable="true"]:empty::before {
  content: "" !important;
  display: inline-block !important;
  width: 0 !important;
  height: 1.3em !important;
}

/* Override any potential width constraints from Yoopta's default styles */
.notion-style-editor .yoopta-block-wrapper,
.notion-style-editor .yoopta-block-content,
.notion-style-editor .yoopta-element,
.notion-style-editor .yoopta-element-wrapper,
.notion-style-editor [data-yoopta-block-wrapper],
.notion-style-editor [data-yoopta-block-content],
.notion-style-editor [data-yoopta-element],
.notion-style-editor [data-yoopta-element-wrapper] {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure all text elements take full width */
.notion-style-editor span,
/* .notion-style-editor div, */
.notion-style-editor section,
.notion-style-editor article {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Special handling for inline elements that should still be inline */
.notion-style-editor strong,
.notion-style-editor em,
.notion-style-editor code,
.notion-style-editor a,
.notion-style-editor mark {
  width: auto !important;
  max-width: none !important;
  display: inline !important;
}

/* Toolbar styling - clean like Notion */
.yoopta-toolbar {
  background: white !important;
  border: 1px solid rgba(55, 53, 47, 0.16) !important;
  border-radius: 6px !important;
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 3px 6px, rgba(15, 15, 15, 0.2) 0px 9px 24px !important;
  padding: 4px !important;
}

.yoopta-toolbar button {
  border: none !important;
  background: transparent !important;
  border-radius: 3px !important;
  padding: 6px 8px !important;
  color: rgb(55, 53, 47) !important;
  font-size: 14px !important;
  transition: background-color 0.1s ease !important;
}

.yoopta-toolbar button:hover {
  background: rgba(55, 53, 47, 0.08) !important;
}

/* Action menu styling - clean like Notion */
.yoopta-action-menu {
  background: white !important;
  border: 1px solid rgba(55, 53, 47, 0.16) !important;
  border-radius: 6px !important;
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 3px 6px, rgba(15, 15, 15, 0.2) 0px 9px 24px !important;
  padding: 4px !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.yoopta-action-menu-item {
  padding: 8px 12px !important;
  border-radius: 3px !important;
  color: rgb(55, 53, 47) !important;
  font-size: 14px !important;
  transition: background-color 0.1s ease !important;
  cursor: pointer !important;
}

.yoopta-action-menu-item:hover,
.yoopta-action-menu-item.selected {
  background: rgba(55, 53, 47, 0.08) !important;
}
