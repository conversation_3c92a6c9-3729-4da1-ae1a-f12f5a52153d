import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSelector, useDispatch } from "react-redux";
import {
  selectIsAuthenticated,
  selectUserData,
  selectUserLoading,
  selectReferrerLoading,
  selectReferrerError,
  selectReferrerSuccess,
} from "@/features/selectors";
import { userActions, referrerActions } from "@/features/rootActions";
import { AppDispatch } from "@/store";
// Removed unused imports since we're replacing hamburger menu with tabs
import DashboardContainer from "./Dashboard";
import MainContainer from "./Main";
import CommissionsContainer from "./Commissions";
import ReferralsContainer from "./Referrals";
import SettingsContainer from "./Settings";
import PayoutsContainer from "./Payouts";

// Define the valid hash sections
type Section =
  | "dashboard"
  | "commissions"
  | "payouts"
  | "referrals"
  | "settings"
  | "";

const ReferralContainer: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const userData = useSelector(selectUserData);
  const isLoadingUser = useSelector(selectUserLoading);

  // Add referrer registration selectors
  const isRegisteringReferrer = useSelector(selectReferrerLoading);
  const referrerError = useSelector(selectReferrerError);
  const referrerSuccess = useSelector(selectReferrerSuccess);

  // Track the current active section based on URL hash
  const [activeSection, setActiveSection] = useState<Section>("");

  // Mobile navigation no longer needs menu state since we're using horizontal tabs

  // Check if user is already an affiliate
  const isReferrer = userData?.isReferrer || false;

  useEffect(() => {
    // Fetch user data to ensure it's available on page refresh
    if (!userData) {
      dispatch(userActions.fetchUserMe());
    }

    // Set initial section based on hash from URL
    const hash = window.location.hash.replace("#", "");

    // Only handle hash logic if we have user data or if there's no hash
    if (userData || !hash) {
      // If user has a hash in URL but is not a referrer, redirect to main affiliate page
      if (hash && userData && !isReferrer) {
        // Use router to navigate to /affiliate without hash
        router.replace("/affiliate");
        setActiveSection("");
      } else {
        setActiveSection((hash as Section) || (isReferrer ? "dashboard" : ""));
      }
    } else if (hash && !userData) {
      // If there's a hash but no user data yet, preserve the hash temporarily
      setActiveSection(hash as Section);
    }

    // Add event listener for hash changes
    const handleHashChange = () => {
      const newHash = window.location.hash.replace("#", "");

      // If user changes to a hash but is not a referrer, redirect to main affiliate page
      // Only redirect if we have user data to confirm they're not a referrer
      if (newHash && userData && !isReferrer) {
        router.replace("/affiliate");
        setActiveSection("");
      } else {
        setActiveSection(
          (newHash as Section) || (isReferrer ? "dashboard" : "")
        );
      }
    };

    window.addEventListener("hashchange", handleHashChange);

    // Cleanup event listener
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
    };
  }, [dispatch, isReferrer, router, userData]);

  // Handle referrer registration success
  useEffect(() => {
    if (referrerSuccess) {
      // Refresh user data to get updated referrer status
      dispatch(userActions.fetchUserMe());
      // The hash will be set to dashboard automatically once isReferrer becomes true
    }
  }, [referrerSuccess, dispatch]);

  // Handler for button clicks
  const handleAffiliateAction = () => {
    if (!isAuthenticated) {
      // Not logged in - redirect to authentication
      router.push("/authentication?redirect=/affiliate");
    } else if (!isReferrer) {
      // Logged in but not an affiliate - register directly as referrer
      dispatch(referrerActions.registerReferrer());
    } else {
      // Already an affiliate - go to dashboard section
      window.location.hash = "dashboard";
    }
  };

  // Navigation items for sidebar
  const navItems = [
    { name: "Dashboard", hash: "dashboard", icon: "📊" },
    { name: "Commissions", hash: "commissions", icon: "💰" },
    { name: "Payouts", hash: "payouts", icon: "💸" },
    { name: "Referrals", hash: "referrals", icon: "🔄" },
    { name: "Settings", hash: "settings", icon: "⚙️" },
  ];

  // Render section content based on activeSection
  const renderSectionContent = () => {
    // Show loading while fetching user data
    if (isLoadingUser && !userData) {
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="absolute inset-0 bg-gray-50 dark:bg-gray-700 bg-opacity-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      );
    }

    // If user is not an affiliate and no specific section is selected, show the landing page
    if (!isReferrer && !activeSection) {
      return (
        <MainContainer
          onJoinClick={handleAffiliateAction}
          isLoading={isRegisteringReferrer}
          error={referrerError}
        />
      );
    }

    // Render specific section content based on activeSection
    switch (activeSection) {
      case "dashboard":
        return <DashboardContainer />;
      case "commissions":
        return <CommissionsContainer />;
      case "payouts":
        return <PayoutsContainer />;
      case "referrals":
        return <ReferralsContainer />;
      case "settings":
        return <SettingsContainer />;
      default:
        // If user is an affiliate but no section is specified, default to dashboard
        if (isReferrer) {
          // Update the hash and render dashboard
          setTimeout(() => {
            window.location.hash = "dashboard";
          }, 0);
          return <DashboardContainer />;
        }
        // Otherwise show main content (already handled above)
        return null;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile Header with Horizontal Tabs */}
      {isAuthenticated && isReferrer && (
        <div className="lg:hidden fixed top-[75px] left-0 right-0 z-40 bg-white dark:bg-gray-800 border-b shadow-sm">
          {/* Horizontal Scrollable Tabs */}
          <div className="relative">
            <div className="overflow-x-auto scrollbar-hide bg-gray-50 dark:bg-gray-900 mobile-tabs-container">
              <div className="flex min-w-max px-2 py-1">
                {navItems.map((item) => (
                  <a
                    key={item.hash}
                    href={`#${item.hash}`}
                    className={`mobile-tab-item flex-shrink-0 flex items-center justify-center px-3 py-3 min-h-[44px] text-sm font-medium transition-all duration-200 border-b-2 rounded-t-lg mx-1 ${
                      activeSection === item.hash
                        ? "border-blue-600 text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 shadow-sm"
                        : "border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white dark:hover:bg-gray-800 hover:shadow-sm"
                    }`}
                    style={{ minWidth: '110px' }}
                  >
                    <span className="mr-2 text-base">{item.icon}</span>
                    <span className="whitespace-nowrap text-xs font-semibold">{item.name}</span>
                  </a>
                ))}
              </div>
            </div>

            {/* Subtle scroll indicators */}
            <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-gray-50 dark:from-gray-900 to-transparent pointer-events-none opacity-60"></div>
            <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-gray-50 dark:from-gray-900 to-transparent pointer-events-none opacity-60"></div>
          </div>
        </div>
      )}

      {/* Desktop Sidebar */}
      {isAuthenticated && isReferrer && (
        <div className="hidden lg:block w-64 border-r bg-white dark:bg-gray-800 shadow-sm">
          {/* Affiliate Program Header */}
          <div className="px-4 py-3 border-b">
            <div className="flex items-center space-x-2">
              <div>
                <h3 className="font-medium text-xl">Affiliate Program</h3>
              </div>
            </div>
          </div>

          {/* User Profile */}
          <div className="px-4 py-4 border-b">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
                {userData?.last_name?.charAt(0) || "T"}
              </div>
              <div>
                <p className="text-sm font-medium">
                  {userData?.first_name || userData?.username}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Menu */}
          <div className="py-2">
            {navItems.map((item) => (
              <a
                key={item.hash}
                href={`#${item.hash}`}
                className={`flex items-center px-4 py-3 text-sm transition-colors ${
                  activeSection === item.hash
                    ? "bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400 font-medium"
                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.name}</span>
              </a>
            ))}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`flex-1 overflow-auto ${isAuthenticated && isReferrer ? 'lg:ml-0 pt-[70px] lg:pt-0' : ''}`}>
        {renderSectionContent()}
      </div>
    </div>
  );
};

export default ReferralContainer;
