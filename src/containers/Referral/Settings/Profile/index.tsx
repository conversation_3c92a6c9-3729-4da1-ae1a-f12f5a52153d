import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUserData,
  selectUserIsUpdating,
  selectUserError,
} from "@/features/selectors";
import { actions as userActions } from "@/features/user/user.slice";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";

interface ProfileFormData {
  email: string;
  firstName: string;
  lastName: string;
  streetAddress: string;
  city: string;
  stateProvince: string;
  zipPostalCode: string;
  country: string;
}

const ProfileSettings: React.FC = () => {
  const dispatch = useDispatch();
  const userData = useSelector(selectUserData);
  const isUpdating = useSelector(selectUserIsUpdating);
  const userError = useSelector(selectUserError);
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const [formData, setFormData] = useState<ProfileFormData>({
    email: "",
    firstName: "",
    lastName: "",
    streetAddress: "",
    city: "",
    stateProvince: "",
    zipPostalCode: "",
    country: "",
  });

  // Populate form with user data when available
  useEffect(() => {
    if (userData) {
      setFormData({
        email: userData.email || "",
        firstName: userData.first_name || "",
        lastName: userData.last_name || "",
        streetAddress: userData.address || "",
        city: userData.city || "",
        stateProvince: userData.state || "",
        zipPostalCode: userData.zip_code || "",
        country: userData.country || "",
      });
    }
  }, [userData]);

  // Handle update completion
  useEffect(() => {
    if (!isUpdating && isLoading) {
      setIsLoading(false);
      if (!userError) {
        setSaveSuccess(true);
        // Hide success message after 3 seconds
        const timer = setTimeout(() => {
          setSaveSuccess(false);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  }, [isUpdating, userError, isLoading]);

  // Handle errors
  useEffect(() => {
    if (userError && isLoading) {
      setIsLoading(false);
    }
  }, [userError, isLoading]);

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear success message when user starts editing
    if (saveSuccess) {
      setSaveSuccess(false);
    }
  };

  const handleSaveChanges = async () => {
    setIsLoading(true);
    setSaveSuccess(false);

    try {
      // Format data according to API requirements (matching onboarding)
      const updateData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        address: formData.streetAddress,
        city: formData.city,
        state: formData.stateProvince,
        country: formData.country,
        zip_code: formData.zipPostalCode,
      };

      // Filter out empty values before sending - only update fields that have values
      const filteredUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([_key, value]) => {
          return (
            value !== null &&
            value !== undefined &&
            value !== "" &&
            (typeof value === "string" ? value.trim() !== "" : true)
          );
        })
      );

      // Check if there are any fields to update
      if (Object.keys(filteredUpdateData).length === 0) {
        console.warn("No fields to update - all fields are empty");
        setIsLoading(false);
        return;
      }

      console.log("Sending profile update with:", filteredUpdateData);

      // Dispatch the same action used in onboarding with filtered data
      dispatch(userActions.updateUserProfile(filteredUpdateData));
    } catch (error) {
      console.error("Error saving profile:", error);
      setIsLoading(false);
    }
  };

  // Helper function to check if form has changes
  const hasChanges = () => {
    if (!userData) return false;

    return (
      formData.firstName !== (userData.first_name || "") ||
      formData.lastName !== (userData.last_name || "") ||
      formData.streetAddress !== (userData.address || "") ||
      formData.city !== (userData.city || "") ||
      formData.stateProvince !== (userData.state || "") ||
      formData.country !== (userData.country || "") ||
      formData.zipPostalCode !== (userData.zip_code || "")
    );
  };

  // Helper function to get changed fields
  const getChangedFields = () => {
    if (!userData) return [];

    const changes = [];
    if (formData.firstName !== (userData.first_name || ""))
      changes.push("First Name");
    if (formData.lastName !== (userData.last_name || ""))
      changes.push("Last Name");
    if (formData.streetAddress !== (userData.address || ""))
      changes.push("Street Address");
    if (formData.city !== (userData.city || "")) changes.push("City");
    if (formData.stateProvince !== (userData.state || ""))
      changes.push("State/Province");
    if (formData.country !== (userData.country || "")) changes.push("Country");
    if (formData.zipPostalCode !== (userData.zip_code || ""))
      changes.push("ZIP/Postal Code");

    return changes;
  };

  return (
    <div className="space-y-8">
      {/* Success/Error Messages */}
      {saveSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
          <p className="text-green-700 dark:text-green-300">
            Profile updated successfully!
          </p>
        </div>
      )}

      {userError && !saveSuccess && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-3" />
          <p className="text-red-700 dark:text-red-300">{userError}</p>
        </div>
      )}

      {/* Show changes preview when there are changes */}
      {hasChanges() && !saveSuccess && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            <strong>Fields to be updated:</strong>{" "}
            {getChangedFields().join(", ")}
          </p>
          <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">
            Only fields with values will be saved. Empty fields will be ignored.
          </p>
        </div>
      )}

      {/* Personal Information Section */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Personal Information
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Update your personal details and contact information.
        </p>

        <div className="space-y-6">
          {/* Email Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
              disabled
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Email cannot be changed. Contact support if needed.
            </p>
          </div>

          {/* First Name & Last Name */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                First Name
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange("firstName", e.target.value)}
                className="w-full px-3 py-3 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base sm:text-sm"
                placeholder="Trinh"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Last Name
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange("lastName", e.target.value)}
                className="w-full px-3 py-3 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base sm:text-sm"
                placeholder="Nguyen"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Address Information Section */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Address Information
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Your address is required for payment processing.
        </p>

        <div className="space-y-6">
          {/* Street Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Street Address
            </label>
            <input
              type="text"
              value={formData.streetAddress}
              onChange={(e) =>
                handleInputChange("streetAddress", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="123 Tech Street"
            />
          </div>

          {/* City & State */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                City
              </label>
              <input
                type="text"
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="San Francisco"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                State/Province
              </label>
              <input
                type="text"
                value={formData.stateProvince}
                onChange={(e) =>
                  handleInputChange("stateProvince", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="CA"
              />
            </div>
          </div>

          {/* ZIP & Country */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ZIP/Postal Code
              </label>
              <input
                type="text"
                value={formData.zipPostalCode}
                onChange={(e) =>
                  handleInputChange("zipPostalCode", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="94105"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Country
              </label>
              <input
                type="text"
                value={formData.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="United States"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSaveChanges}
          disabled={isLoading || isUpdating || !hasChanges()}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading || isUpdating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving Changes...
            </>
          ) : (
            `Save Changes${
              hasChanges() ? ` (${getChangedFields().length})` : ""
            }`
          )}
        </button>
        {!hasChanges() && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            No changes to save
          </p>
        )}
      </div>
    </div>
  );
};

export default ProfileSettings;
