import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import type { NextApiRequest, NextApiResponse } from "next";

export interface ReferrerLink {
  id: string;
  documentId?: string;
  name: string;
  url: string;
  short_link?: string;
  visitors: number;
  leads: number;
  conversions: number;
  // Additional view tracking fields (visitors is the primary field)
  direct_page_views?: number;
  referrer_link_views?: number;
  short_link_views?: number;
  referrer_sources?: Record<string, number>;
  // Page relationship
  page?: {
    id: number;
    documentId: string;
    title: string;
    slug: string;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ReferrerLink | AppError>
) {
  const token = req.headers.authorization?.split(" ")[1];
  const { id } = req.query;

  if (!token) {
    return res.status(401).json({
      statusCode: 401,
      message: "Authentication required",
    });
  }

  if (!id || typeof id !== "string") {
    return res.status(400).json({
      statusCode: 400,
      message: "Link ID is required",
    });
  }

  // The id from the URL is actually the documentId from Strapi
  const documentId = id;

  try {
    if (req.method === "PUT") {
      // Update a referrer link
      const { name, url, shortLink, selectedPage } = req.body;

      // Pass documentId to StrapiClient instead of id
      const response = await StrapiClient.updateReferrerLink(
        documentId,
        { name, url, shortLink, selectedPage },
        token
      );
      return res.status(200).json(response.data);
    } else if (req.method === "DELETE") {
      // Pass documentId to StrapiClient instead of id
      await StrapiClient.deleteReferrerLink(documentId, token);
      return res.status(204).end();
    }

    return res
      .status(405)
      .json({ statusCode: 405, message: "Method not allowed" });
  } catch (error: any) {
    console.error(`Referrer link ${documentId} API error:`, error);
    return sendApiError(
      res,
      error,
      `Error processing referrer link ${documentId} request`
    );
  }
}
